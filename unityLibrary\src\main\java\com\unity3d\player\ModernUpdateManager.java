package com.unity3d.player;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.lifecycle.Observer;
import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.Data;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkInfo;
import androidx.work.WorkManager;

/**
 * Modern update manager with improved error handling, retry mechanism, and UI
 * Replaces the legacy UpdateManager with modern Android components
 */
public class ModernUpdateManager {

    private static final String TAG = "ModernUpdateManager";
    private static final String DEFAULT_APK_NAME = "app-update.apk";
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long INITIAL_BACKOFF_DELAY = 10; // seconds

    private Context applicationContext;  // 用于非UI操作
    private WeakReference<Context> activityContextRef;  // 用于UI操作
    private String downloadUrl;
    private String version;
    private String content;
    private ModernProgressDialog progressDialog;
    private NetworkMonitor networkMonitor;
    private Handler mainHandler;
    private UUID currentWorkId;
    private Observer<WorkInfo> workObserver;
    private UUID lastWorkId; // 保存最后一个workId用于清理

    // Retry mechanism
    private int retryCount = 0;
    private boolean isRetryEnabled = true;

    // Callbacks
    private UpdateCallback updateCallback;

    public interface UpdateCallback {
        void onUpdateStarted();

        void onProgressUpdate(int progress);

        void onUpdateCompleted(String filePath);

        void onUpdateFailed(String error);

        void onUpdateCancelled();
    }

    public ModernUpdateManager(Context context) {
        this.applicationContext = context.getApplicationContext();
        this.activityContextRef = new WeakReference<>(context);

        // 使用ApplicationContext初始化不需要UI的组件
        this.networkMonitor = new NetworkMonitor(applicationContext);
        this.mainHandler = new Handler(Looper.getMainLooper());
        setupNetworkMonitoring();
    }

    // 获取UI操作的Context的辅助方法
    private Context getUIContext() {
        Context activityContext = activityContextRef.get();
        // 如果Activity Context已被回收，返回null或ApplicationContext
        // 注意：返回ApplicationContext可能导致UI操作失败，但至少不会崩溃
        return activityContext != null ? activityContext : null;
    }

    // 检查UI Context是否可用
    private boolean isUIContextAvailable() {
        return activityContextRef.get() != null;
    }

    private void setupNetworkMonitoring() {
        networkMonitor.startMonitoring(new NetworkMonitor.NetworkStateListener() {
            @Override
            public void onNetworkAvailable() {
                // Network is back, we could resume download if needed
                if (progressDialog != null && progressDialog.isShowing()) {
                    mainHandler.post(() -> {
                        progressDialog.setMessage("Network restored. Continuing download...");
                    });
                }
            }

            @Override
            public void onNetworkLost() {
                // Network lost during download
                if (progressDialog != null && progressDialog.isShowing()) {
                    mainHandler.post(() -> {
                        progressDialog.setMessage("Network lost. Will retry when connection is restored...");
                    });
                }
            }
        });
    }

    public void setDownloadUrl(String url) {
        this.downloadUrl = url;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setUpdateCallback(UpdateCallback callback) {
        this.updateCallback = callback;
    }

    public void setRetryEnabled(boolean enabled) {
        this.isRetryEnabled = enabled;
    }

    /**
     * Check for updates and show dialog
     */
    public void checkUpdate(boolean isForced) {
        if (!networkMonitor.isNetworkAvailable()) {
            showToast("Network is unavailable. Please check your connection.");
            if (updateCallback != null) {
                updateCallback.onUpdateFailed("No network connection");
            }
            return;
        }

        // 检查存储权限（仅在Android 6.0+需要，且仅当targetSdk < 30时需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
            Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            if (!hasStoragePermission()) {
                showToast("Storage permission is required for downloading updates.");
                if (updateCallback != null) {
                    updateCallback.onUpdateFailed("Storage permission not granted");
                }
                return;
            }
        }

        showUpdateDialog(isForced);
    }

    /**
     * Check if the app has storage permission
     */
    private boolean hasStoragePermission() {
        return ContextCompat.checkSelfPermission(applicationContext,
                Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
    }

    private void showUpdateDialog(boolean isForced) {
//        AlertDialog.Builder builder = new AlertDialog.Builder(context)
//                .setTitle("New Version Available")
//                .setMessage("A new version is available. Would you like to update now?")
//                .setPositiveButton("Update", (dialog, which) -> startDownload());
//
//        if (!isForced) {
//            builder.setNegativeButton("Later", (dialog, which) -> {
//                if (updateCallback != null) {
//                    updateCallback.onUpdateCancelled();
//                }
//            });
//        }
//
//        builder.setCancelable(!isForced);
//        builder.show();
        Context uiContext = getUIContext();
        if (uiContext == null) {
            // UI Context不可用，记录日志并放弃显示对话框
            System.out.println("Cannot show update dialog: UI Context is not available");
            return;
        }
        UpdateDialog updateDialog = new UpdateDialog(uiContext);
        TextView tv_version = updateDialog.findViewById(R.id.tv_version);
        TextView tv_update_content = updateDialog.findViewById(R.id.tv_update_content);
        tv_version.setText("V" + version);
        tv_update_content.setText(content);
        Button btnUpdateNow = updateDialog.findViewById(R.id.btn_update_now);
        Button btnNotNow = updateDialog.findViewById(R.id.btn_not_now);
        LinearLayout notupdate_layout = updateDialog.findViewById(R.id.notupdate_layout);
        Button ex_now = updateDialog.findViewById(R.id.ex_now);
        if (isForced) {
            notupdate_layout.setVisibility(View.GONE);
            ex_now.setVisibility(View.VISIBLE);
        } else {
            notupdate_layout.setVisibility(View.VISIBLE);
            ex_now.setVisibility(View.GONE);
        }
        btnUpdateNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
                startDownload();
            }
        });
        ex_now.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateDialog.dismiss();
                startDownload();
            }
        });
        btnNotNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (updateCallback != null) {
                    updateCallback.onUpdateCancelled();
                }
                updateDialog.dismiss();
            }
        });
        updateDialog.show();
    }

    /**
     * Start the download process using WorkManager
     */
    private void startDownload() {
        if (downloadUrl == null || downloadUrl.isEmpty()) {
            showToast("Download URL not set");
            if (updateCallback != null) {
                updateCallback.onUpdateFailed("Download URL not set");
            }
            return;
        }

        // Cancel any existing download
        cancelCurrentDownload();

        // Reset retry count
        retryCount = 0;

        // Show progress dialog
        showProgressDialog();

        // Start download work
        startDownloadWork();

        if (updateCallback != null) {
            updateCallback.onUpdateStarted();
        }
    }

    private void startDownloadWork() {
        System.out.println("ModernUpdateManager: Starting download work for URL: " + downloadUrl);

        // 确保没有正在运行的下载任务
        if (currentWorkId != null) {
            System.out.println("ModernUpdateManager: Cancelling existing work ID: " + currentWorkId);
            WorkManager.getInstance(applicationContext).cancelWorkById(currentWorkId);
        }

        // Create input data
        Data inputData = new Data.Builder()
                .putString(DownloadWorker.KEY_DOWNLOAD_URL, downloadUrl)
                .putString(DownloadWorker.KEY_FILE_NAME, DEFAULT_APK_NAME)
                .build();

        // Create constraints - 使用CONNECTED但避免过于严格的约束
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED) // 需要网络连接
                .setRequiresBatteryNotLow(false) // 不要求电池电量
                .build();

        // Create work request - 禁用自动重试，由我们手动控制
        OneTimeWorkRequest downloadRequest = new OneTimeWorkRequest.Builder(DownloadWorker.class)
                .setInputData(inputData)
                .setConstraints(constraints)
                // 移除BackoffCriteria，禁用WorkManager的自动重试机制
                // .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, INITIAL_BACKOFF_DELAY, TimeUnit.SECONDS)
                .build();

        currentWorkId = downloadRequest.getId();
        lastWorkId = currentWorkId;
        System.out.println("ModernUpdateManager: Created new work request with ID: " + currentWorkId);

        // Enqueue work
        WorkManager.getInstance(applicationContext).enqueue(downloadRequest);
        System.out.println("ModernUpdateManager: Work enqueued successfully");

        // Observe work progress
        observeWorkProgress();
    }

    private void observeWorkProgress() {
        if (currentWorkId == null) {
            System.out.println("ModernUpdateManager: Cannot observe progress - currentWorkId is null");
            return;
        }
        System.out.println("ModernUpdateManager: Starting to observe work progress for ID: " + currentWorkId);
        // 先清理旧的观察者，避免重复注册
        cleanupObserver();
        workObserver = new Observer<WorkInfo>() {
            @Override
            public void onChanged(WorkInfo workInfo) {
                if (workInfo == null) {
                    System.out.println("ModernUpdateManager: Received null WorkInfo");
                    return;
                }
                System.out.println("ModernUpdateManager: WorkInfo state changed to: " + workInfo.getState());
                handleWorkInfoUpdate(workInfo);
            }
        };

        WorkManager.getInstance(applicationContext)
                .getWorkInfoByIdLiveData(currentWorkId)
                .observeForever(workObserver);
        System.out.println("ModernUpdateManager: Observer registered for work ID: " + currentWorkId);
    }

    private void handleWorkInfoUpdate(WorkInfo workInfo) {
        System.out.println("ModernUpdateManager: Handling work info update - State: " + workInfo.getState() +
                          ", Work ID: " + workInfo.getId());
        switch (workInfo.getState()) {
            case RUNNING:
                Data progress = workInfo.getProgress();
                int progressValue = progress.getInt(DownloadWorker.KEY_PROGRESS, 0);
                System.out.println("ModernUpdateManager: Download progress: " + progressValue + "%");
                updateProgress(progressValue);
                break;

            case SUCCEEDED:
                Data outputData = workInfo.getOutputData();
                String filePath = outputData.getString(DownloadWorker.KEY_FILE_PATH);
                System.out.println("ModernUpdateManager: Download succeeded, file path: " + filePath);
                handleDownloadSuccess(filePath);
                break;

            case FAILED:
                Data errorData = workInfo.getOutputData();
                String errorMessage = errorData.getString(DownloadWorker.KEY_ERROR_MESSAGE);
                System.out.println("ModernUpdateManager: Download failed with error: " + errorMessage);
                handleDownloadFailure(errorMessage);
                break;

            case CANCELLED:
                System.out.println("ModernUpdateManager: Download was cancelled");
                handleDownloadCancellation();
                break;

            case ENQUEUED:
                System.out.println("ModernUpdateManager: Download was enqueued");
                break;

            case BLOCKED:
                System.out.println("ModernUpdateManager: Download is blocked (waiting for constraints)");
                break;
        }
    }

    private void updateProgress(int progress) {
        mainHandler.post(() -> {
            if (progressDialog != null) {
                progressDialog.setProgress(progress);
            }
            if (updateCallback != null) {
                updateCallback.onProgressUpdate(progress);
            }
        });
    }

    private void handleDownloadSuccess(String filePath) {
        mainHandler.post(() -> {
            dismissProgressDialog();
            if (filePath != null) {
                installApk(filePath);
                if (updateCallback != null) {
                    updateCallback.onUpdateCompleted(filePath);
                }
            }
        });
        if (currentWorkId != null) {
            lastWorkId = currentWorkId;
        }
        cleanupObserver();
    }

    private void handleDownloadFailure(String errorMessage) {
        mainHandler.post(() -> {
            if (isRetryEnabled && retryCount < MAX_RETRY_ATTEMPTS) {
                retryCount++;
                showRetryDialog(errorMessage);
            } else {
                dismissProgressDialog();
                showToast("Download failed: " + (errorMessage != null ? errorMessage : "Unknown error"));
                if (updateCallback != null) {
                    updateCallback.onUpdateFailed(errorMessage);
                }
            }
        });
        if (currentWorkId != null) {
            lastWorkId = currentWorkId;
        }
        cleanupObserver();
    }

    private void handleDownloadCancellation() {
        mainHandler.post(() -> {
            dismissProgressDialog();
            if (updateCallback != null) {
                updateCallback.onUpdateCancelled();
            }
        });
        if (currentWorkId != null) {
            lastWorkId = currentWorkId;
        }
        cleanupObserver();
    }

    private void showRetryDialog(String errorMessage) {
        String message = String.format("Download failed: %s\n\nRetry attempt %d of %d",
                errorMessage != null ? errorMessage : "Unknown error",
                retryCount,
                MAX_RETRY_ATTEMPTS);
        Context uiContext = getUIContext();
        if (uiContext == null) {
            // UI Context不可用，记录日志并放弃显示对话框
            System.out.println("Cannot show update dialog: UI Context is not available");
            return;
        }
        new AlertDialog.Builder(uiContext)
                .setTitle("Download Failed")
                .setMessage(message)
                .setPositiveButton("Retry", (dialog, which) -> {
                    // Retry download - 使用startDownloadWork而不是startDownload，避免重置retryCount
                    // 但需要先取消现有的下载任务
                    cancelCurrentDownload();
                    startDownloadWork();
                })
                .setNegativeButton("Cancel", (dialog, which) -> {
                    dismissProgressDialog();
                    if (updateCallback != null) {
                        updateCallback.onUpdateFailed("User cancelled after retry");
                    }
                })
                .setCancelable(false)
                .show();
    }

    private boolean isProgressDialogContextValid() {
        // 检查progressDialog的context是否仍然有效
        return progressDialog != null && getUIContext() != null;
    }

    private void showProgressDialog() {
        // 检查UI Context可用性
        if (!isUIContextAvailable()) {
            System.out.println("Cannot show progress dialog: UI Context is not available");
            return;
        }
        Context uiContext = getUIContext();
        if (progressDialog == null || !isProgressDialogContextValid()) {
            progressDialog = new ModernProgressDialog(uiContext);
            progressDialog.setTitle("Downloading Update");
            progressDialog.setMessage("Please wait while the update is being downloaded...");
            progressDialog.setCancelable(false);

            // Set custom progress bar colors
            // You can customize these colors as needed
            progressDialog.setProgressColor(0xFF4A73FD);        // Green progress
            progressDialog.setProgressBackgroundColor(0xFFE0E0E0); // Light gray background

//            progressDialog.setOnCancelListener(() -> {
//                cancelCurrentDownload();
//                if (updateCallback != null) {
//                    updateCallback.onUpdateCancelled();
//                }
//            });
        }

        progressDialog.setProgress(0);
        progressDialog.show();
    }

    private void dismissProgressDialog() {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
        }
    }

    private void cancelCurrentDownload() {
        UUID workIdToCancel = currentWorkId;
        if (workIdToCancel != null) {
            lastWorkId = workIdToCancel;
            WorkManager.getInstance(applicationContext).cancelWorkById(workIdToCancel);
            currentWorkId = null;
        }
        cleanupObserver();
    }


    private void cleanupObserver() {
        if (workObserver != null) {
            // 优先使用 currentWorkId，如果为空则使用 lastWorkId
            UUID workIdToCleanup = currentWorkId != null ? currentWorkId : lastWorkId;
            if (workIdToCleanup != null) {
                try {
                    WorkManager.getInstance(applicationContext)
                            .getWorkInfoByIdLiveData(workIdToCleanup)
                            .removeObserver(workObserver);
                } catch (Exception e) {
                    System.err.println("Error removing observer: " + e.getMessage());
                }
            }
            workObserver = null;
        }
    }

    private void installApk(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                showToast("Downloaded file not found");
                return;
            }
            Intent intent = new Intent(Intent.ACTION_VIEW);
            Uri fileUri = getFileUri(file);
            intent.setDataAndType(fileUri, "application/vnd.android.package-archive");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            applicationContext.startActivity(intent);
        } catch (Exception e) {
            showToast("Failed to install APK: " + e.getMessage());
            if (updateCallback != null) {
                updateCallback.onUpdateFailed("Installation failed: " + e.getMessage());
            }
        }
    }

    private Uri getFileUri(File file) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return FileProvider.getUriForFile(applicationContext,
                    applicationContext.getPackageName() + ".fileprovider", file);
        } else {
            return Uri.fromFile(file);
        }
    }

    private void showToast(String message) {
        mainHandler.post(() -> {
            Context uiContext = getUIContext();
            if (uiContext != null) {
                Toast.makeText(uiContext, message, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * Clean up resources when the manager is no longer needed
     */
    public void cleanup() {
        cancelCurrentDownload();
        dismissProgressDialog();
        networkMonitor.stopMonitoring();
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
        // 清除Context引用
        activityContextRef.clear();
        currentWorkId = null;
        lastWorkId = null;
    }

    /**
     * Get current network status
     */
    public boolean isNetworkAvailable() {
        return networkMonitor.isNetworkAvailable();
    }

    /**
     * Get network type description
     */
    public String getNetworkType() {
        return networkMonitor.getNetworkType();
    }
}
